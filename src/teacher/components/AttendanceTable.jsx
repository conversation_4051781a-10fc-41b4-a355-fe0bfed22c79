import React from "react";
import AttendanceHeader from "./AttendanceHeader";
import AttendanceTableHeader from "./AttendanceTableHeader";
import AttendanceRow from "./AttendanceRow";

const AttendanceTable = () => {
  const attendanceData = [
    {id:1, name: "<PERSON><PERSON><PERSON> <PERSON>", status: "Present", date: "19/11/2024" },
    {id:2, name: "<PERSON><PERSON><PERSON> <PERSON>", status: "Absent", date: "19/11/2024" },
    {id:3, name: "<PERSON><PERSON><PERSON> <PERSON>", status: "Absent", date: "19/11/2024" },
    {id:4, name: "<PERSON><PERSON><PERSON> <PERSON>", status: "Absent", date: "19/11/2024" },
    {id:5, name: "<PERSON><PERSON><PERSON> <PERSON>", status: "Present", date: "19/11/2024" },
    {id:6, name: "<PERSON><PERSON><PERSON> <PERSON>", status: "Present", date: "19/11/2024" },
    {id:7, name: "<PERSON><PERSON><PERSON> Daniel", status: "Present", date: "19/11/2024" },
    { id:8, name: "<PERSON><PERSON><PERSON> <PERSON>", status: "Late", date: "19/11/2024" },
  ];

  return (
    <div className="max-w-full mx-auto bg-white shadow rounded-xl p-4 ">
      <AttendanceHeader />
      <AttendanceTableHeader />

      <div className="divide-y rounded-b-md overflow-hidden flex flex-col gap-2 mt-2 border rounded-2xl p-4 border-[#A3AED0]">
        {attendanceData.map((item, index) => (
          <AttendanceRow
            key={index}
            name={item.name}
            status={item.status}
            date={item.date}
          />
        ))}
      </div>
    </div>
  );
};

export default AttendanceTable;

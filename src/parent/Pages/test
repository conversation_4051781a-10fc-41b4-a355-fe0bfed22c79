 <!-- {currentStep === 2 && (
          <div className="flex flex-col lg:flex-row gap-8 items-center justify-center min-h-[400px]">
            {/* Payment Details Form */}
            <div className="w-full max-w-md">
              <div className="text-xl font-semibold mb-2 text-center lg:text-left">
                Step 3: Payment Details Entry
              </div>
              <div className="text-sm text-gray-500 mb-4 text-center lg:text-left">
                Securely Enter Your Payment Details Below to Complete Your Transaction
              </div>
              <form className="bg-white rounded-xl shadow p-6 mb-6 flex flex-col gap-4">
                <div>
                  <label className="block text-xs text-gray-500 mb-1">Name on Card</label>
                  <input
                    type="text"
                    className="w-full border focus:outline-none border-gray-300 rounded px-3 py-2"
                    placeholder="<PERSON><PERSON>cilla <PERSON>"
                    // defaultValue={selectedStudent}
                    value={cardName}
                    onChange={e => setCardName(e.target.value)}
                  />
                </div>
                <div>
                  <label className="block text-xs text-gray-500 mb-1">Card Number</label>
                  <input
                    className="w-full border focus:outline-none border-gray-300 rounded px-3 py-2"
                    placeholder="5679 7378 4037 2122"
                    maxLength={19}
                    inputMode="numeric"
                    value={cardNumber}
                    onChange={handleCardNumberChange}
                    autoComplete="cc-number"
                    required
                  />
                </div>
                <div className="flex gap-2">
                  <div className="flex-1">
                    <label className="block text-xs text-gray-500 mb-1">Expiration Date</label>
                    <input
                      type="text"

                      className="w-full border focus:outline-none border-gray-300 rounded px-3 py-2"
                      placeholder="MM / YY"
                      maxLength={7}
                      inputMode="numeric"
                      value={expiry}
                      onChange={handleExpiryChange}
                      autoComplete="cc-exp"
                      required
                    />
                  </div>
                  <div className="flex-1">
                    <label className="block text-xs text-gray-500 mb-1">CVV</label>
                    <input
                      type="password"
                      className="w-full border focus:outline-none border-gray-300 rounded px-3 py-2"
                      placeholder="CVV"
                      maxLength={5}
                      inputMode="numeric"
                      value={cvv}
                      onChange={handleCvvChange}
                      autoComplete="cc-csc"
                      required
                    />
                  </div>
                </div>
                <div className="flex items-center justify-between mt-2">
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={saveMethod}
                      onChange={e => setSaveMethod(e.target.checked)}
                      className="form-checkbox rounded border-gray-300"
                    />
                    <span className="text-xs text-gray-600">Save payment method</span>
                  </label>
                  <label className="flex items-center gap-2">
                    <input
                      type="radio"
                      checked
                      readOnly
                      className="form-radio rounded border-gray-300"
                    />
                    <span className="text-xs text-gray-600">Add New Card</span>
                  </label>
                </div>
              </form>
              {/* Payment Summary */}
              <div className="bg-white rounded-xl shadow p-4 mb-4">
                <div className="flex justify-between text-sm mb-2">
                  <span>Subtotal</span>
                  <span>${fee ? fee.price : "100.00"}</span>
                </div>
                <div className="flex justify-between text-sm mb-2">
                  <span>Discount/ Scholarship</span>
                  <span>$0.00</span>
                </div>
                <div className="flex justify-between text-sm mb-2">
                  <span>Service Charge</span>
                  <span>$0.00</span>
                </div>
                <div className="flex justify-between text-base font-semibold mt-2">
                  <span className="text-[#1736a4]">Total Amount Due</span>
                  <span className="text-[#1736a4]">${fee ? fee.price : "100.00"}</span>
                </div>
              </div>
              <div className="flex gap-4 items-center justify-between">
                <button
                  className="w-full bg-[#1736a4] text-white py-3 rounded-lg font-semibold hover:bg-blue-900 transition"
                  onClick={handleReturn}
                  type="button"
                >
                  Previous
                </button>
                <button
                  className={`w-full bg-[#1736a4] text-white py-3 rounded-lg font-semibold hover:bg-blue-900 transition ${!isCardFormValid ? "opacity-50 cursor-not-allowed" : ""}`}
                  onClick={handleProceed}
                  type="button"
                  disabled={!isCardFormValid}
                >
                  Review Payment
                </button>
              </div>

            </div>
            {/* Card Illustration */}
            <div className="w-full max-w-md flex justify-center">
              <img
                src="/bankcard.png"
                alt="Cards Illustration"
                loading="lazy"
                className="rounded-2xl w-full max-w-xs"
              />
            </div>
          </div>
        )} -->

<!-- const paymentMethods = [
  {
    value: "card",
    label: "Credit Card/Debit Card",
    icon: "/mastercard.png", // Replace with your icon path
  },
  {
    value: "bank",
    label: "Bank Transfer",
    icon: "/Yandex.png",
  },
  {
    value: "paypal",
    label: "Paypal",
    icon: "/paypal.png",
  },
  {
    value: "stripe",
    label: "Stripe",
    icon: "/stripe.png",
  },
]; -->

        {/* Step 3: Payment Details Entry or Bank/PayPal/Stripe UI */}
        <!-- {currentStep === 2 && (
          <>
            {/* Bank Transfer UI */}
            {selectedMethod === "bank" && (
              <div className="w-full max-w-2xl mx-auto bg-white rounded-xl shadow p-6 flex flex-col gap-6 items-center">
                <div className="text-center text-sm text-gray-700 mb-2">
                  Kindly pay into any bank of your choice via bank transfer
                </div>
                <div className="w-full flex flex-col gap-3">
                  {bankAccounts.map((acc) => (
                    <div key={acc.account} className="flex items-center justify-between bg-blue-50 rounded-lg px-4 py-3">
                      <div className="flex items-center gap-3">
                        <img src={acc.logo} alt={acc.bank} className="w-10 h-10 object-contain" />
                        <div>
                          <div className="font-semibold text-[#1736a4] text-base">
                            <span className="text-[#1736a4] hover:underline cursor-pointer">{acc.account}</span> {acc.bank}
                          </div>
                          <div className="text-xs text-gray-600">{acc.name}</div>
                        </div>
                      </div>
                      <button
                        className="flex items-center gap-1 text-[#1736a4] text-sm font-medium hover:underline"
                        onClick={() => navigator.clipboard.writeText(acc.account)}
                        type="button"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" strokeWidth={2} viewBox="0 0 24 24">
                          <rect x="9" y="9" width="13" height="13" rx="2" stroke="currentColor" />
                          <rect x="3" y="3" width="13" height="13" rx="2" stroke="currentColor" />
                        </svg>
                        Copy
                      </button>
                    </div>
                  ))}
                </div>
                <button
                  className="w-full bg-[#1736a4] text-white py-3 rounded-lg font-semibold hover:bg-blue-900 transition mt-4"
                  onClick={handleReturn}
                  type="button"
                >
                  Go Back
                </button>
              </div>
            )}

            {/* PayPal UI */}
            {selectedMethod === "paypal" && (
              <div className="w-full max-w-md mx-auto bg-white rounded-xl shadow p-8 flex flex-col items-center gap-6">
                <img src="/paypal.png" alt="PayPal" className="w-24 mb-2" />
                <div className="text-lg font-semibold text-center mb-2">Pay with PayPal</div>
                <div className="text-gray-600 text-center mb-4">
                  You will be redirected to PayPal to complete your payment securely.
                </div>
                <button
                  className="w-full bg-yellow-400 text-black py-3 rounded-lg font-bold flex items-center justify-center gap-2 hover:bg-yellow-500 transition"
                  onClick={() => alert("Redirect to PayPal (integration needed)")}
                  type="button"
                >
                  <img src="/paypal.png" alt="PayPal" className="w-6" />
                  Pay with PayPal
                </button>
                <button
                  className="w-full bg-[#1736a4] text-white py-3 rounded-lg font-semibold hover:bg-blue-900 transition"
                  onClick={handleReturn}
                  type="button"
                >
                  Previous
                </button>
              </div>
            )}

            {/* Stripe UI */}
            {selectedMethod === "stripe" && (
              <div className="w-full max-w-md mx-auto bg-white rounded-xl shadow p-8 flex flex-col items-center gap-6">
                <img src="/stripe.png" alt="Stripe" className="w-24 mb-2" />
                <div className="text-lg font-semibold text-center mb-2">Pay with Stripe</div>
                <div className="text-gray-600 text-center mb-4">
                  Enter your card details below to pay securely with Stripe.
                </div>
                {/* Stripe Card Form (mockup, replace with real Stripe Elements for production) */}
                <form className="w-full flex flex-col gap-4">
                  <input
                    type="text"
                    className="w-full border border-gray-300 rounded px-3 py-2"
                    placeholder="Card Number"
                    disabled
                    value="•••• •••• •••• ••••"
                  />
                  <div className="flex gap-2">
                    <input
                      type="text"
                      className="w-full border border-gray-300 rounded px-3 py-2"
                      placeholder="MM / YY"
                      disabled
                      value="MM / YY"
                    />
                    <input
                      type="text"
                      className="w-full border border-gray-300 rounded px-3 py-2"
                      placeholder="CVC"
                      disabled
                      value="CVC"
                    />
                  </div>
                  <button
                    className="w-full bg-[#635bff] text-white py-3 rounded-lg font-bold hover:bg-[#5046e5] transition"
                    onClick={e => { e.preventDefault(); alert("Stripe payment (integration needed)"); }}
                    type="submit"
                  >
                    Pay with Stripe
                  </button>
                </form>
                <button
                  className="w-full bg-[#1736a4] text-white py-3 rounded-lg font-semibold hover:bg-blue-900 transition"
                  onClick={handleReturn}
                  type="button"
                >
                  Previous
                </button>
              </div>
            )}

            {/* Card UI (default) */}
            {(selectedMethod === "card" || !["bank", "paypal", "stripe"].includes(selectedMethod)) && (
              <div className="flex flex-col lg:flex-row gap-8 items-center justify-center min-h-[400px]">
                {/* Payment Details Form */}
                <div className="w-full max-w-md">
                  <div className="text-xl font-semibold mb-2 text-center lg:text-left">
                    Step 3: Payment Details Entry
                  </div>
                  <div className="text-sm text-gray-500 mb-4 text-center lg:text-left">
                    Securely Enter Your Payment Details Below to Complete Your Transaction
                  </div>
                  <form className="bg-white rounded-xl shadow p-6 mb-6 flex flex-col gap-4">
                    <div>
                      <label className="block text-xs text-gray-500 mb-1">Name on Card</label>
                      <input
                        type="text"
                        className="w-full border focus:outline-none border-gray-300 rounded px-3 py-2"
                        placeholder="Priscilla Daniel"
                        value={cardName}
                        onChange={e => setCardName(e.target.value)}
                      />
                    </div>
                    <div>
                      <label className="block text-xs text-gray-500 mb-1">Card Number</label>
                      <input
                        className="w-full border focus:outline-none border-gray-300 rounded px-3 py-2"
                        placeholder="5679 7378 4037 2122"
                        maxLength={19}
                        inputMode="numeric"
                        value={cardNumber}
                        onChange={handleCardNumberChange}
                        autoComplete="cc-number"
                        required
                      />
                    </div>
                    <div className="flex gap-2">
                      <div className="flex-1">
                        <label className="block text-xs text-gray-500 mb-1">Expiration Date</label>
                        <input
                          type="text"
                          className="w-full border focus:outline-none border-gray-300 rounded px-3 py-2"
                          placeholder="MM / YY"
                          maxLength={7}
                          inputMode="numeric"
                          value={expiry}
                          onChange={handleExpiryChange}
                          autoComplete="cc-exp"
                          required
                        />
                      </div>
                      <div className="flex-1">
                        <label className="block text-xs text-gray-500 mb-1">CVV</label>
                        <input
                          type="password"
                          className="w-full border focus:outline-none border-gray-300 rounded px-3 py-2"
                          placeholder="CVV"
                          maxLength={5}
                          inputMode="numeric"
                          value={cvv}
                          onChange={handleCvvChange}
                          autoComplete="cc-csc"
                          required
                        />
                      </div>
                    </div>
                    <div className="flex items-center justify-between mt-2">
                      <label className="flex items-center gap-2">
                        <input
                          type="checkbox"
                          checked={saveMethod}
                          onChange={e => setSaveMethod(e.target.checked)}
                          className="form-checkbox rounded border-gray-300"
                        />
                        <span className="text-xs text-gray-600">Save payment method</span>
                      </label>
                      <label className="flex items-center gap-2">
                        <input
                          type="radio"
                          checked
                          readOnly
                          className="form-radio rounded border-gray-300"
                        />
                        <span className="text-xs text-gray-600">Add New Card</span>
                      </label>
                    </div>
                  </form>
                  {/* Payment Summary */}
                  <div className="bg-white rounded-xl shadow p-4 mb-4">
                    <div className="flex justify-between text-sm mb-2">
                      <span>Subtotal</span>
                      <span>${fee ? fee.price : "100.00"}</span>
                    </div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>Discount/ Scholarship</span>
                      <span>$0.00</span>
                    </div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>Service Charge</span>
                      <span>$0.00</span>
                    </div>
                    <div className="flex justify-between text-base font-semibold mt-2">
                      <span className="text-[#1736a4]">Total Amount Due</span>
                      <span className="text-[#1736a4]">${fee ? fee.price : "100.00"}</span>
                    </div>
                  </div>
                  <div className="flex gap-4 items-center justify-between">
                    <button
                      className="w-full bg-[#1736a4] text-white py-3 rounded-lg font-semibold hover:bg-blue-900 transition"
                      onClick={handleReturn}
                      type="button"
                    >
                      Previous
                    </button>
                    <button
                      className={`w-full bg-[#1736a4] text-white py-3 rounded-lg font-semibold hover:bg-blue-900 transition ${!isCardFormValid ? "opacity-50 cursor-not-allowed" : ""}`}
                      onClick={handleProceed}
                      type="button"
                      disabled={!isCardFormValid}
                    >
                      Review Payment
                    </button>
                  </div>
                </div>
                {/* Card Illustration */}
                <div className="w-full hidden lg:block max-w-md justify-center">
                  <img
                    src="/bankcard.png"
                    alt="Cards Illustration"
                    loading="lazy"
                    className="rounded-2xl w-full max-w-xs"
                  />
                </div>
              </div>
            )}
          </>
        )}
        {/* Step 4: Double Check Information */}
        {currentStep === 3 && (
          <div className="flex flex-col lg:flex-row gap-8 items-center justify-center min-h-[400px]">
            {/* Review Payment Details */}
            <div className="w-full max-w-md">
              <div className="text-xl font-semibold mb-2 text-center lg:text-left">
                Step 4: Review Payment Details
              </div>
              <div className="text-sm text-gray-500 mb-4 text-center lg:text-left">
                Double-check your payment details before confirming to ensure a smooth transaction.
              </div>
              <div className="bg-white rounded-xl shadow p-4 mb-4">
                <div className="flex justify-between text-sm mb-2">
                  <span>Fee</span>
                  <span className="font-semibold">{fee ? fee.title : "Tuition Fee"}</span>
                </div>
                <div className="flex justify-between text-sm mb-2">
                  <span>Selected Payment Method</span>
                  <span className="capitalize font-semibold">{selectedMethod || "Card"}</span>
                </div>
                <div className="flex justify-between text-sm mb-2">
                  <span>Additional Charges</span>
                  <span>$0.00</span>
                </div>
              </div>
              <label className="flex items-center gap-2 mb-4">
                <input
                  type="checkbox"
                  checked={!!reviewAgreed}
                  onChange={e => setReviewAgreed(e.target.checked)}
                  className="form-checkbox rounded border-gray-300"
                />
                <span className="text-xs text-gray-600">
                  I agree to the terms and conditions, including any applicable service fees, and authorize the payment as outlined above.
                </span>
              </label>

              <div className="flex gap-4 items-center justify-between">
                <button
                  className="w-full bg-[#1736a4] text-white py-3 rounded-lg font-semibold hover:bg-blue-900 transition"
                  onClick={handleReturn}
                  type="button"
                >
                  Previous
                </button>
                <button
                  className="w-full bg-[#1736a4] text-white py-3 rounded-lg font-semibold hover:bg-blue-900 transition disabled:opacity-50 disabled:cursor-not-allowed"
                  onClick={handleProceed}
                  type="button"
                  disabled={!reviewAgreed}
                >
                  Confirm & Pay
                </button>
              </div>

            </div>
            {/* Card Illustration */}
            <div className="w-full hidden lg:block max-w-md justify-center">
              <img
                src="/bankcard.png"
                alt="Cards Illustration"
                loading="lazy"
                className="rounded-2xl w-full max-w-xs"
              />
            </div>
          </div>
        )} -->